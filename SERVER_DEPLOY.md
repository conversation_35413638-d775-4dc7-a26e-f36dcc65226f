# 🚀 服务器部署指南

## 📋 部署文件说明

### 1. **docker-compose.yml** - 开发/测试环境
- 包含应用和 Nginx 反向代理
- 支持 Traefik 标签（如果使用 Traefik）
- 健康检查和日志配置

### 2. **docker-compose.prod.yml** - 生产环境
- 轻量级配置，只包含应用
- 资源限制和安全配置
- 适合直接部署到服务器

### 3. **nginx.conf** - Nginx 配置
- HTTPS 重定向
- SSL/TLS 配置
- 静态文件缓存
- 安全头设置

### 4. **deploy.sh** - 自动化部署脚本
- 一键部署到不同环境
- 依赖检查和状态监控
- 资源清理功能

## 🛠️ 服务器准备

### 1. 安装 Docker 和 Docker Compose

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 重新登录以应用组权限
```

### 2. 上传项目文件

```bash
# 方法一：使用 git
git clone https://github.com/your-username/gobang-game.git
cd gobang-game

# 方法二：使用 scp
scp -r ./gobang-game user@server:/path/to/deployment/
```

### 3. 配置域名和 SSL（可选）

```bash
# 修改 nginx.conf 中的域名
sed -i 's/your-domain.com/actual-domain.com/g' nginx.conf

# 创建 SSL 证书目录
mkdir -p ssl

# 使用 Let's Encrypt 获取证书
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/key.pem
```

## 🚀 部署方法

### 方法一：使用部署脚本（推荐）

```bash
# 给脚本执行权限
chmod +x deploy.sh

# 部署到生产环境
./deploy.sh prod

# 部署到开发环境
./deploy.sh dev

# 查看状态
./deploy.sh status

# 清理资源
./deploy.sh cleanup
```

### 方法二：手动部署

#### 生产环境部署

```bash
# 构建镜像
docker build -t gobang-game:latest .

# 启动生产环境
docker-compose -f docker-compose.prod.yml up -d

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

#### 开发环境部署

```bash
# 启动开发环境
docker-compose up -d --build

# 查看日志
docker-compose logs -f
```

## 🔧 配置选项

### 环境变量

在 `docker-compose.prod.yml` 中可以配置：

```yaml
environment:
  - NODE_ENV=production
  - NEXT_TELEMETRY_DISABLED=1
  - PORT=3100
  - HOSTNAME=0.0.0.0
  # 添加其他环境变量
```

### 端口映射

```yaml
ports:
  - "80:3100"    # HTTP
  - "443:443"    # HTTPS (如果使用 Nginx)
  - "8080:80"    # 备用端口
```

### 资源限制

```yaml
deploy:
  resources:
    limits:
      cpus: '1.0'      # CPU 限制
      memory: 512M     # 内存限制
    reservations:
      cpus: '0.5'      # CPU 预留
      memory: 256M     # 内存预留
```

## 📊 监控和维护

### 查看容器状态

```bash
# 查看运行中的容器
docker ps

# 查看所有容器
docker ps -a

# 查看容器资源使用
docker stats
```

### 查看日志

```bash
# 实时日志
docker-compose -f docker-compose.prod.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.prod.yml logs -f gobang-game

# 查看最近 100 行日志
docker-compose -f docker-compose.prod.yml logs --tail=100
```

### 健康检查

```bash
# 检查应用健康状态
curl http://localhost/health

# 检查容器健康状态
docker inspect --format='{{.State.Health.Status}}' gobang-game-prod
```

### 更新部署

```bash
# 拉取最新代码
git pull

# 重新构建和部署
./deploy.sh prod

# 或手动更新
docker-compose -f docker-compose.prod.yml down
docker build -t gobang-game:latest .
docker-compose -f docker-compose.prod.yml up -d
```

## 🔒 安全配置

### 防火墙设置

```bash
# Ubuntu UFW
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable

# CentOS/RHEL firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### SSL 证书自动续期

```bash
# 添加到 crontab
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   sudo netstat -tulpn | grep :80
   sudo kill -9 <PID>
   ```

2. **容器启动失败**
   ```bash
   docker-compose -f docker-compose.prod.yml logs gobang-game
   ```

3. **内存不足**
   ```bash
   free -h
   docker system prune -a
   ```

4. **磁盘空间不足**
   ```bash
   df -h
   docker system prune -a --volumes
   ```

### 备份和恢复

```bash
# 备份镜像
docker save gobang-game:latest | gzip > gobang-game-backup.tar.gz

# 恢复镜像
gunzip -c gobang-game-backup.tar.gz | docker load
```

## 📈 性能优化

### 1. 启用 HTTP/2 和 Gzip
- Nginx 配置已包含 HTTP/2 和 Gzip 压缩

### 2. 静态文件缓存
- 配置了 1 年的静态文件缓存

### 3. 容器资源限制
- 防止单个容器占用过多资源

### 4. 日志轮转
- 配置了日志文件大小和数量限制

## 🌐 访问应用

部署完成后，可以通过以下方式访问：

- **HTTP**: http://your-server-ip
- **HTTPS**: https://your-domain.com (如果配置了 SSL)
- **健康检查**: http://your-server-ip/health

## 📞 支持

如果遇到问题，请检查：
1. 容器日志
2. 系统资源使用情况
3. 网络连接
4. 防火墙设置
