# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建输出
.next/
out/
build/
dist/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
*.lcov

# 依赖锁定文件（保留 pnpm-lock.yaml）
package-lock.json
yarn.lock

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git 相关
.git
.gitignore

# 测试相关
.nyc_output
coverage

# 其他
README.md
Dockerfile
.dockerignore
