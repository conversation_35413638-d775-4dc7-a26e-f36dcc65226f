@import 'tailwindcss';
@import '@fortawesome/fontawesome-free/css/all.css';

@theme {
    --color-primary: #8b5a2b;
    --color-secondary: #d2b48c;
    --color-board: #deb887;
    --color-black: #000000;
    --color-white: #ffffff;

    --font-sans: Inter, system-ui, sans-serif;
}

@layer utilities {
    .content-auto {
        content-visibility: auto;
    }
    .board-grid {
        background-size: 100% 100%;
        background-image:
            linear-gradient(to right, rgba(0, 0, 0, 0.6) 1px, transparent 1px),
            linear-gradient(to bottom, rgba(0, 0, 0, 0.6) 1px, transparent 1px);
    }
    .piece-shadow {
        filter: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));
    }
    .piece-transition {
        transition: all 0.2s ease-out;
    }
    .btn-hover {
        transition: all 0.2s ease;
    }
    .btn-hover:hover {
        transform: translateY(-2px);
        box-shadow:
            0 10px 15px -3px rgba(0, 0, 0, 0.1),
            0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
}
