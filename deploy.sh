#!/bin/bash

# 五子棋游戏服务器部署脚本
# 使用方法: ./deploy.sh [环境]
# 环境选项: dev, prod

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查镜像是否存在
check_image() {
    local image_name="gobang-game:1.0.1"
    if docker image inspect $image_name >/dev/null 2>&1; then
        log_success "镜像 $image_name 已存在"
        return 0
    else
        log_error "镜像 $image_name 不存在，请先下载镜像"
        log_info "下载命令示例: docker pull your-registry/gobang-game:1.0.1"
        log_info "或重新标记: docker tag existing-image gobang-game:1.0.1"
        exit 1
    fi
}

# 构建镜像（可选）
build_image() {
    log_info "构建 Docker 镜像..."
    docker build -t gobang-game:1.0.1 .
    log_success "镜像构建完成"
}

# 部署开发环境
deploy_dev() {
    log_info "部署开发环境..."
    docker-compose down --remove-orphans
    docker-compose up -d --build
    log_success "开发环境部署完成"
    log_info "访问地址: http://localhost:3100"
}

# 部署生产环境
deploy_prod() {
    log_info "部署生产环境..."

    # 检查镜像
    check_image

    # 停止现有容器
    docker-compose -f docker-compose.prod.yml down --remove-orphans

    # 启动生产环境
    docker-compose -f docker-compose.prod.yml up -d

    log_success "生产环境部署完成"
    log_info "访问地址: http://your-server-ip"
}

# 部署到服务器（使用已有镜像）
deploy_server() {
    log_info "部署到服务器..."

    # 检查镜像
    check_image

    # 停止现有容器
    docker-compose -f docker-compose.server.yml down --remove-orphans 2>/dev/null || true

    # 启动服务器环境
    docker-compose -f docker-compose.server.yml up -d

    log_success "服务器部署完成"
    log_info "访问地址: http://your-server-ip"
}

# 显示状态
show_status() {
    log_info "容器状态:"
    docker ps --filter "name=gobang" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    log_info "日志查看命令:"
    echo "  开发环境: docker-compose logs -f"
    echo "  生产环境: docker-compose -f docker-compose.prod.yml logs -f"
}

# 清理资源
cleanup() {
    log_info "清理未使用的 Docker 资源..."
    docker system prune -f
    docker image prune -f
    log_success "清理完成"
}

# 主函数
main() {
    local env=${1:-dev}
    
    echo "🎮 五子棋游戏部署脚本"
    echo "======================="
    
    check_dependencies
    
    case $env in
        "dev")
            deploy_dev
            ;;
        "prod")
            deploy_prod
            ;;
        "server")
            deploy_server
            ;;
        "status")
            show_status
            return
            ;;
        "cleanup")
            cleanup
            return
            ;;
        *)
            log_error "未知环境: $env"
            echo "使用方法: $0 [dev|prod|server|status|cleanup]"
            echo ""
            echo "环境说明:"
            echo "  dev    - 开发环境（需要构建）"
            echo "  prod   - 生产环境（需要构建）"
            echo "  server - 服务器环境（使用已有镜像 gobang-game:1.0.1）"
            echo "  status - 查看状态"
            echo "  cleanup - 清理资源"
            exit 1
            ;;
    esac
    
    echo ""
    show_status
    
    echo ""
    log_success "部署完成! 🎉"
}

# 执行主函数
main "$@"
