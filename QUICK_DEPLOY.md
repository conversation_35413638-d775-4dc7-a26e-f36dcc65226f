# 🚀 快速服务器部署指南

## 📋 前提条件

你已经在服务器上有了 `gobang-game:1.0.1` 镜像。

## 🎯 快速部署步骤

### 1. 上传配置文件到服务器

只需要上传这些文件到服务器：
```
docker-compose.server.yml
deploy.sh
```

或者上传所有文件：
```bash
# 方法一：使用 scp
scp docker-compose.server.yml deploy.sh user@server:/path/to/deployment/

# 方法二：使用 git（如果服务器有网络）
git clone https://github.com/your-repo/gobang-game.git
cd gobang-game
```

### 2. 一键部署

```bash
# 给脚本执行权限
chmod +x deploy.sh

# 部署到服务器（使用已有镜像）
./deploy.sh server
```

### 3. 验证部署

```bash
# 查看容器状态
./deploy.sh status

# 或手动查看
docker ps

# 检查应用是否正常
curl http://localhost
```

## 🔧 手动部署（如果不使用脚本）

```bash
# 1. 停止现有容器（如果有）
docker-compose -f docker-compose.server.yml down

# 2. 启动新容器
docker-compose -f docker-compose.server.yml up -d

# 3. 查看日志
docker-compose -f docker-compose.server.yml logs -f
```

## 📊 管理命令

### 查看状态
```bash
# 查看容器状态
docker ps

# 查看资源使用
docker stats gobang-game-server

# 查看健康状态
docker inspect --format='{{.State.Health.Status}}' gobang-game-server
```

### 查看日志
```bash
# 实时日志
docker-compose -f docker-compose.server.yml logs -f

# 最近 100 行日志
docker-compose -f docker-compose.server.yml logs --tail=100

# 只看错误日志
docker-compose -f docker-compose.server.yml logs | grep ERROR
```

### 重启服务
```bash
# 重启容器
docker-compose -f docker-compose.server.yml restart

# 或使用脚本
./deploy.sh server
```

### 停止服务
```bash
# 停止容器
docker-compose -f docker-compose.server.yml down

# 停止并删除数据
docker-compose -f docker-compose.server.yml down -v
```

## 🌐 访问应用

部署成功后，可以通过以下方式访问：

- **HTTP**: `http://your-server-ip`
- **主页**: `http://your-server-ip`
- **五子棋游戏**: `http://your-server-ip/gobang`

## 🔧 配置说明

### 端口配置
- 容器内端口：`3100`
- 映射到主机端口：`80`
- 如果 80 端口被占用，可以修改为其他端口：
  ```yaml
  ports:
    - "8080:3100"  # 改为 8080 端口
  ```

### 资源限制
当前配置：
- CPU 限制：1 核心
- 内存限制：512MB
- CPU 预留：0.5 核心
- 内存预留：256MB

### 环境变量
```yaml
environment:
  - NODE_ENV=production
  - NEXT_TELEMETRY_DISABLED=1
  - PORT=3100
  - HOSTNAME=0.0.0.0
```

## 🚨 故障排除

### 1. 端口被占用
```bash
# 查看端口占用
sudo netstat -tulpn | grep :80

# 停止占用端口的进程
sudo kill -9 <PID>

# 或修改端口映射
```

### 2. 镜像不存在
```bash
# 检查镜像
docker images | grep gobang-game

# 如果镜像名称不对，重新标记
docker tag existing-image-name gobang-game:1.0.1
```

### 3. 容器启动失败
```bash
# 查看详细错误
docker-compose -f docker-compose.server.yml logs gobang-game

# 检查容器状态
docker inspect gobang-game-server
```

### 4. 健康检查失败
```bash
# 手动测试健康检查
docker exec gobang-game-server wget --no-verbose --tries=1 --spider http://localhost:3100

# 查看健康检查日志
docker inspect --format='{{.State.Health}}' gobang-game-server
```

## 📈 性能监控

### 资源使用监控
```bash
# 实时监控
docker stats gobang-game-server

# 系统资源
htop
free -h
df -h
```

### 日志监控
```bash
# 监控错误日志
docker-compose -f docker-compose.server.yml logs -f | grep -i error

# 监控访问日志
docker-compose -f docker-compose.server.yml logs -f | grep -E "GET|POST"
```

## 🔄 更新部署

当有新版本镜像时：

```bash
# 1. 下载新镜像
docker pull your-registry/gobang-game:1.0.2

# 2. 重新标记
docker tag your-registry/gobang-game:1.0.2 gobang-game:1.0.1

# 3. 重新部署
./deploy.sh server
```

## 📞 支持

如果遇到问题：

1. 检查容器日志
2. 验证镜像是否存在
3. 确认端口没有被占用
4. 检查系统资源是否充足

常用调试命令：
```bash
# 进入容器调试
docker exec -it gobang-game-server sh

# 查看容器详细信息
docker inspect gobang-game-server

# 查看网络连接
docker port gobang-game-server
```
